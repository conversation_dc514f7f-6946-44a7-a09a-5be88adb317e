/**
 * 存储过程功能说明请勿删除
 * 存储过程实现功能：入口DB2F-各业务线取值语句生成
 1. 拼接20个db2f业务线的sql语句 STL_CONFIG_DB2F
 2. 将拼接好的语句更新至db2f业务线配置表
 3. 将更新好的db2f业务线配置表内容插入db2f任务表
**/
DROP PROCEDURE IF EXISTS stlusers.P_SETTLE_DB2F;
DELIMITER //
CREATE OR REPLACE  DEFINER="stlusers"@"10.%" PROCEDURE "P_SETTLE_DB2F"(
    inMonth          IN   VARCHAR2,
    batch            IN   VARCHAR2,
    flag_version     IN   VARCHAR2,
    reserve1         IN   VARCHAR2,
    reserve2         IN   VARCHAR2,
    proc_out         OUT  VARCHAR2,
    outSysError      OUT  VARCHAR2,
    outReturn        OUT  NUMBER,
    outBL            OUT  NUMBER,
    outAR            OUT  NUMBER
)
AS
    iv_Sql VARCHAR2(5000);
    iv_Sql_101       VARCHAR2(2048);
    iv_Sql_102       VARCHAR2(2048);
    iv_Sql_103       VARCHAR2(2048);
    iv_Sql_104       VARCHAR2(2048);
    iv_Sql_105       VARCHAR2(2048);
    iv_Sql_106       VARCHAR2(2048);
    iv_Sql_107       VARCHAR2(5000);
    iv_Sql_108       VARCHAR2(2048);
    iv_Sql_201       VARCHAR2(2048);
    iv_Sql_202       VARCHAR2(2048);
    iv_Sql_203       VARCHAR2(5000);
    iv_Sql_204       VARCHAR2(2048);
    iv_Sql_205       VARCHAR2(7000);
    iv_Sql_206       VARCHAR2(2048);
    -- iv_Sql_207       VARCHAR2(2048);  跟翀昊确认 已经不用了 不生成任务 208的sql 此前就没更新了 by 王小东 20231216
    -- iv_Sql_208       VARCHAR2(2048);
    iv_Sql_209       VARCHAR2(2048);
    iv_Sql_210       VARCHAR2(4096);
    iv_Sql_211       VARCHAR2(16000);
    iv_Sql_212       VARCHAR2(2048);

    v_proc_name   VARCHAR2(30) := 'P_SETTLE_DB2F';

    iv_fRate NUMBER;
    iv_Batch VARCHAR2(1);
    iv_Str  VARCHAR2(500);
    P_ERRCODE   VARCHAR2(16);
    P_ERRMSG    VARCHAR2(1024);
BEGIN
    outSysError := 'OK';
    outReturn := 0;

    if (batch = '0') then
       iv_Batch := '1';
    elsif (batch in ('1', '2')) then
       iv_Batch := batch;
else
       outSysError := '批次错误。现仅支持预出账-0；一批-1；二批-2。';
       outReturn := '-1';
       return;
end if;


    DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
GET DIAGNOSTICS CONDITION 1
    P_ERRCODE = RETURNED_SQLSTATE, P_ERRMSG = MESSAGE_TEXT;
    outSysError := substr(P_ERRMSG, 1, 1000);
    outReturn  := -1;
ROLLBACK;

select ('exception: ' || outReturn || '|'  || P_ERRCODE || '|' || outSysError ) AS error_msg ;
call LOG_PROCEDURES(outSysError,v_proc_name);
END;


BEGIN

-- set @vSql := 'delete from STLUSERS.LOG_PROCEDURES where MODULE = ''' || v_proc_name || '''';
-- SELECT @vSql;
-- PREPARE STMT FROM @vSql;
-- EXECUTE STMT;
-- DEALLOCATE PREPARE STMT;
-- COMMIT;

call LOG_PROCEDURES('开始执行@iv_Sql_101',v_proc_name);

        set @iv_Sql_101 := 'select ''1'' as sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lce ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''2'' ' ||
             'and (tt.pospecnumber not in (''0102001'', ''*********'', ''*********'', ''50025'') ' ||
              'or (tt.pospecnumber = ''0102001'' and tt.feetype not in (''33'', ''83'')))' ||
              'and (tt.sospecnumber not in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'') ' ||
                  'or sospecnumber is null) ' ||
            'union all '||
            'select ''1'' as sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.list_price orgfee, ' ||
                  'tt.list_price notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lce ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''2'' ' ||
             'and  tt.sospecnumber  in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')' ;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_101
WHERE a.CONF_ID = 101;


SELECT @iv_Sql_101;
call LOG_PROCEDURES('执行@iv_Sql_101结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_102',v_proc_name);

        set @iv_Sql_102 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''3'' ' ||
             'and (tt.pospecnumber not in (''0102001'', ''*********'', ''*********'', ''50025'', ''50022'') ' ||
              'or (tt.pospecnumber = ''0102001'' and tt.feetype not in (''33'', ''83''))) ' ||
              'and (tt.sospecnumber not in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ||
                  'or sospecnumber is null) ' ||
           'union all ' ||
           'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.list_price orgfee, ' ||
                  'tt.list_price notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
           'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''3'' ' ||
             'and  tt.sospecnumber  in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_102
WHERE a.CONF_ID = 102;

SELECT @iv_Sql_102;
call LOG_PROCEDURES('@iv_Sql_102执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_103',v_proc_name);

        set @iv_Sql_103 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
           'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and (tt.partid=''0'' or tt.partid is null) ' ||
             'and (tt.pospecnumber not in (''0102001'', ''*********'', ''*********'', ''50025'', ''50022'') ' ||
              'or (tt.pospecnumber = ''0102001'' and tt.feetype not in (''33'', ''83'')) ' ||
              ') ' ||
              'and (tt.sospecnumber not in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ||
                  'or sospecnumber is null) ' ||
          'union all '||
             'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.list_price orgfee, ' ||
                  'tt.list_price notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and (tt.partid=''0'' or tt.partid is null) ' ||
             'and  tt.sospecnumber  in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_103
WHERE a.CONF_ID = 103;

SELECT @iv_Sql_103;
call LOG_PROCEDURES('@iv_Sql_103执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_104',v_proc_name);

        set @iv_Sql_104 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''1'' ' ||
             'and (tt.pospecnumber not in (''0102001'', ''*********'', ''*********'', ''50025'', ''50022'')' ||
              'or (tt.pospecnumber = ''0102001'' and tt.feetype not in (''33'', ''83''))) ' ||
              'and  (tt.sospecnumber not in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT  ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ||
                  'or sospecnumber is null) ' ||
            'union all '||
            'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.list_price orgfee, ' ||
                  'tt.list_price notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lcf ' ||
            'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.partid=''1'' ' ||
             'and  tt.sospecnumber  in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'')   ' ;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_104
WHERE a.CONF_ID = 104;


SELECT @iv_Sql_104;
call LOG_PROCEDURES('@iv_Sql_104执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_105',v_proc_name);

        set @iv_Sql_105 := 'select ''1'' sn, ' ||
                   'to_char(aa.stream_id) stream_id, ' ||
                   'aa.customernumber, ' ||
                   'aa.ordermode, ' ||
                   'aa.pospecnumber, ' ||

                   'decode(aa.soid, '''', aa.soid,null, aa.soid, aa.sospecnumber) sospecnumber, ' ||
                   'to_char(aa.poid) poid, ' ||
                   'to_char(aa.soid) soid, ' ||
                   'aa.dn, ' ||
                   'aa.accountid, ' ||
                   'aa.feetype, ' ||
                   'aa.amount orgfee, ' ||
                   'aa.amount notaxfee, ' ||
                   '0 taxfee, ' ||
                   '''6'' taxrate, ' ||
                   'aa.orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'aa.orgmonth || ''********'' start_time, ' ||
                   '''1'' phase, ' ||
                   '''BL'' filesource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' lcd, ' ||

                   ''''' lce ' ||
              'from sync_bl_settle_' || inMonth || ' aa ' ||
               'where (aa.pospecnumber not in (''*********'', ''*********'', ''*********'', ''*********'', ''*********'', ''*********'', ''60000'') and aa.sospecnumber <> ''110151'')' ||
                   'and (aa.pospecnumber <> ''50021'') ' || --智能路由
                   'and (aa.sospecnumber <> ''5003401'' or aa.feetype <> ''157'') ' ||  --5G终端
                   'and (aa.sospecnumber <> ''****************'' or aa.feetype not like ''1%'') ' ||-- --排除云MAS受理模式1
                   'and aa.sospecnumber <> ''5001606''   '||
                   'and aa.status = ''0'' ' ||
              'union all ' ||
                'select ''1'' sn, ' ||
                   'max(to_char(aa.stream_id)) stream_id, ' ||
                   'aa.customernumber, ' ||
                   'aa.ordermode, ' ||
                   'aa.pospecnumber, ' ||

                   'decode(aa.soid, '''', aa.soid, aa.sospecnumber) sospecnumber, ' ||
                   'to_char(aa.poid) poid, ' ||
                   'to_char(aa.soid) soid, ' ||
                   'aa.dn, ' ||
                   'aa.accountid, ' ||
                   'aa.feetype, ' ||
                   'sum(aa.amount) orgfee, ' ||
                   'sum(aa.amount) notaxfee, ' ||
                   '0 taxfee, ' ||
                   '''9'' taxrate, ' ||
                   'aa.orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'aa.orgmonth || ''********'' start_time, ' ||
                   '''1'' phase, ' ||
                   '''BL'' filesource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' lcd, ' ||

                   ''''' lce ' ||
              'from sync_bl_settle_' || inMonth || ' aa ' ||
               'where aa.pospecnumber in (''50021'') ' ||-- --智能路由
                 'and aa.status = ''0'' ' ||
              'group by orgmonth, customernumber, ordermode, pospecnumber, sospecnumber, poid, soid, dn, accountid, feetype';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_105
WHERE a.CONF_ID = 105;

SELECT @iv_Sql_105;
call LOG_PROCEDURES('@iv_Sql_105执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_106',v_proc_name);

        set @iv_Sql_106 := 'select ''5'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
				  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  ''''' lcb, ' ||

                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource filesource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca' ||
            ' from sync_interface_bl_' || inMonth || ' tt ' ||
           ' where tt.status = 0 and (tt.pospecnumber in ' ||
                  '(select aa.offer_code ' ||
                     'from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm = ''CP'' ' ||
                      'and aa.column_nm = ''offer_code'') ' ||
                       'or tt.sospecnumber in ' ||
                  '(select aa.product_code ' ||
                     'from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm = ''CP'' ' ||
                      'and aa.column_nm = ''product_code''))';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_106
WHERE a.CONF_ID = 106;


SELECT @iv_Sql_106;
call LOG_PROCEDURES('@iv_Sql_106执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_107',v_proc_name);

         set @iv_Sql_107 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
				  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
            ' from sync_interface_bl_' || inMonth || ' tt ' ||
            'left join (select * from stl_mnp_record_poc m where m.settlemonth = ' || inMonth ||
                                 ' and m.partid = substr(''' || inMonth || ''', 5, 2)) ss ' ||
              'on tt.dn = ss.member_code and tt.soid = ss.prod_order_id ' ||
           'where tt.remark=''2'' ' ||
             'and tt.status = 0 ' ||
             'and tt.pospecnumber = ''50025'' ' ||
             'union all ' ||
            'select /*+ no_index(tt idx_pospecnumber)*/  ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
            ' from int_interface_bl tt ' ||
           ' where tt.remark=''2'' and tt.orgmonth = ''' || inMonth || ''' ' ||
             'and tt.status = 0 and tt.pospecnumber <> ''50118'' ' ||
             'union all ' ||
             'select /*+ no_index(tt idx_po_so)*/  ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'tt.orgmonth || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  ''''' adjmonth, ' ||
                  ''''' lca ' ||
            ' from sync_interface_amount_' || inMonth || ' tt ' ||
           'where tt.remark=''2'' ' ||
             'and tt.status = 0 and tt.pospecnumber <> ''50118'' ' ||
        'union all ' ||
          -- sdwan接口表  专线卫视DICT
             'select ''1'' sn,    '||
                  'tt.orgbid stream_id,   '||
                  'tt.customernumber,   '||
                  'tt.ordermode,     '||
                  'tt.pospecnumber,    '||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber,   '||
                  'tt.poid,   '||
                  'tt.soid,   '||
                  'decode(tt.dn,''0'','''',tt.dn) dn, '||
                  'tt.accountid,  '||
                  'tt.feetype,  '||
                  'tt.orgfee,   '||
                  'tt.notaxfee,    '||
                  'tt.taxfee,   '||
                  'tt.taxrate,   '||
                  'tt.orgmonth,      '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase,  '||
                  'tt.datasource,  '||
                  'to_char(sysdate, ''yyyymmdd'') ndate,  '||
                  'tt.adjmonth,  '||
                  '''''   lca  '||
             ' from stludr.sync_interface_sdwan_' || inMonth || ' tt   '||
              'where tt.sospecnumber=''****************'' and tt.status=''0''  and tt.remark=''2'' '||
        'union all ' ||
          -- sdwan接口表  省间部分费项映射
             'select ''1'' sn,    '||
                  'tt.orgbid stream_id,   '||
                  'tt.customernumber,   '||
                  'tt.ordermode,     '||
                  'tt.pospecnumber,    '||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber,   '||
                  'tt.poid,   '||
                  'tt.soid,   '||
                  'decode(tt.dn,''0'','''',tt.dn) dn, '||
                  'tt.accountid,  '||
                  'decode(tt.feetype,''3682'',''362'',''3683'',''363'',''3684'',''364'',''3685'',''365'',''3865'',''568'') feetype,  '||
                  'tt.orgfee,   '||
                  'tt.notaxfee,    '||
                  'tt.taxfee,   '||
                  'tt.taxrate,   '||
                  'tt.orgmonth,      '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase,  '||
                  'tt.datasource,  '||
                  'to_char(sysdate, ''yyyymmdd'') ndate,  '||
                  'tt.adjmonth,  '||
                  '''''   lca  '||
             ' from stludr.sync_interface_sdwan_' || inMonth || ' tt   '||
              'where tt.sospecnumber=''910401'' and tt.status=''0''  and tt.remark=''2'' ';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_107
WHERE a.CONF_ID = 107;

SELECT @iv_Sql_107;
call LOG_PROCEDURES('@iv_Sql_107执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_108',v_proc_name);

        set @iv_Sql_108 := 'select ''1'' sn,   '||
                  'to_char(b.stream_id) stream_id, ' ||
                  'b.customernumber,   '||
                  'b.ordermode,     '||
                  'b.pospecnumber,   '||
                  'decode(b.soid, '''', b.soid,null, b.soid, b.sospecnumber) sospecnumber, '||
                  'b.poid,    '||
                  'b.soid,     '||
                  'decode(b.dn,''0'','''',b.dn) dn,    '||
                  'b.accountid,   '||
                  'b.feetype,   '||
                  'b.amount orgfee,    '||
                  'b.amount notaxfee,   '||
                  '0,     '||
                  '''6'',    '||
                  'b.orgmonth,   '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'b.orgmonth || ''********'' start_time, ' ||
                  '''1'' phase,   '||
                  'b.datasource,   '||
                  'to_char(sysdate, ''yyyymmdd'') ndate,    '||
                  'b.prov_cd,   '|| -- ----21
                  ''''' lca, ' ||
                  ''''' lcb   '||
            'from stludr.sync_bl_settle_' || inMonth || ' b  '||
        'where (b.pospecnumber=''50034'' and b.sospecnumber=''5003401'' and b.feetype=''157'') '||
            'or (b.pospecnumber = ''50024'' and b.sospecnumber = ''****************'' and b.feetype like ''1%'') ' ||
            'or (b.pospecnumber = ''50016'' and b.sospecnumber = ''5001606'') ' ||
			'or (b.pospecnumber between ''*********'' and ''*********'' or b.pospecnumber = ''60000'' or b.sospecnumber = ''110151'') ' || -- 云MAS受理模式3、5


                  -- 跨省专线卫士   省间
        'union all   '||
                 'select ''1'' sn,    '||
                  'to_char(b.orgbid) stream_id,  '||
                  'b.customernumber,  '||
                  'b.ordermode,     '||
                  'b.pospecnumber,    '||
                  'decode(b.soid, '''', b.soid,null, b.soid, b.sospecnumber) sospecnumber,  '||
                  'b.poid,   '||
                  'b.soid,    '||
                  'decode(b.dn,''0'','''',b.dn) dn,  '||
                  'b.accountid,   '||
                  'b.feetype,   '||
                  'b.notaxfee orgfee,   '||
                  'b.notaxfee notaxfee,    '||
                  '0,    '||
                  '''6'',   '||
                  'b.orgmonth,  '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'b.orgmonth || ''********'' start_time,   '||
                  '''1'' phase,   '||
                  'b.datasource,   '||
                  'to_char(sysdate, ''yyyymmdd'') ndate,   '||
                  'b.provcd,   '||-- ----21
                  ''''' lca,   '||
                  '''''  lcb  '||
            'from stludr.int_interface_bl b   '||
            'where b.pospecnumber=''50118'' and  b.remark=''2'' '||
            -- 跨省专线卫士  省专
         'union all   '||
                 'select ''1'' sn,  '||
                  'to_char(b.orgbid) stream_id,  '||
                  'b.customernumber,  '||
                  'b.ordermode,   '||
                  'b.pospecnumber,   '||
                  'decode(b.soid, '''', b.soid,null, b.soid, b.sospecnumber) sospecnumber,  '||
                  'b.poid,   '||
                  'b.soid,    '||
                  'decode(b.dn,''0'','''',b.dn) dn,  '||
                  'b.accountid, '||
                  'b.feetype, '||
                  'b.notaxfee orgfee,  '||
                  'b.notaxfee notaxfee,  '||
                  '0,    '||
                  '''6'',   '||
                  'b.orgmonth,   '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'b.orgmonth || ''********'' start_time,  '||
                  '''1'' phase,  '||
                  'b.datasource,   '||
                  'to_char(sysdate, ''yyyymmdd'') ndate, '||
                  'b.provcd,  '|| -- ----21
                  ''''' lca, '||
                  '''''  lcb '||
            'from stludr.sync_interface_amount_' || inMonth || ' b   '||
             'where  b.pospecnumber=''50118'' and b.remark=''2'' ';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_108
WHERE a.CONF_ID = 108;


SELECT @iv_Sql_108;
call LOG_PROCEDURES('@iv_Sql_108执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_201',v_proc_name);

        set @iv_Sql_201 := 'select ''7'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn, ''0'', '''', tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.paymonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'tt.orgmonth || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  ''''' lca ' ||
             'from sync_interface_ar_' || inMonth || ' tt ' ||
            'where tt.status = 0 and ((tt.soid is null and ' ||
                   'tt.poid in ' ||
                   '(select aa.offer_order_id ' ||
                      'from stlusers.STL_CONFIG_DB2F aa ' ||
                     'where aa.type_nm = ''SA'' ' ||
                       'and tt.orgmonth between aa.eff_month and aa.exp_month)) ' ||
                'or (tt.soid is not null and ' ||
                    'tt.soid in ' ||
                   '(select aa.product_order_id ' ||
                      'from stlusers.STL_CONFIG_DB2F aa ' ||
                     'where aa.type_nm = ''SA'' ' ||
                       'and tt.orgmonth between aa.eff_month and aa.exp_month)))';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_201
WHERE a.CONF_ID = 201;

SELECT @iv_Sql_201;
call LOG_PROCEDURES('@iv_Sql_201执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_202',v_proc_name);

        set @iv_Sql_202 := 'select ''6'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.paymonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
             ' from sync_interface_ar_' || inMonth || ' tt ' ||
            'where tt.status = 0 and (tt.pospecnumber in ' ||
                  '(select aa.offer_code ' ||
                     'from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm = ''CP'' ' ||
                      'and aa.column_nm = ''offer_code'') ' ||
                       'or tt.sospecnumber in ' ||
                  '(select aa.product_code ' ||
                     'from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm = ''CP'' ' ||
                      'and aa.column_nm = ''product_code'')) ' ||
            'union ' ||
            'select ''6'' sn, ' ||
                   'bb.orgbid stream_id, ' ||
                   'bb.customernumber, ' ||
                   'bb.ordermode, ' ||
                   'bb.pospecnumber, ' ||
                   'decode(bb.soid, '''', bb.soid,null, bb.soid, bb.sospecnumber) sospecnumber, ' ||
                   'bb.poid, ' ||
                   'bb.soid, ' ||
                   'decode(bb.dn,''0'','''',bb.dn) dn, ' ||
                   'bb.accountid, ' ||
                   'bb.feetype, ' ||
                   'bb.orgfee, ' ||
                   'bb.notaxfee, ' ||
                   'bb.taxfee , ' ||
                   'bb.taxrate, ' ||
                   'bb.orgmonth, ' ||
                    'decode(bb.paymonth, '''',''' || inMonth || ''',null,''' || inMonth || ''',bb.paymonth) paymonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'decode(bb.adjmonth,'''', bb.orgmonth,null, bb.orgmonth, adjmonth) || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   'bb.datasource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   'bb.adjmonth, ' ||
                   ''''' lca ' ||
              ' from sync_interface_ar_' || inMonth || ' bb ' ||
             'where bb.status = 0 and bb.pospecnumber in ' ||
                   '(select aa.offer_code ' ||
                      'from stlusers.STL_CONFIG_DB2F aa ' ||
                     'where aa.type_nm = ''CAR'' ' ||
                       'and aa.column_nm = ''offer_code'')';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_202
WHERE a.CONF_ID = 202;


SELECT @iv_Sql_202;
call LOG_PROCEDURES('@iv_Sql_202执行结束',v_proc_name);

END;


BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_203',v_proc_name);

        set @iv_Sql_203 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
             ' from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' ' ||
              'and tt.status = 0 ' ||
              'and ((tt.pospecnumber not in ' ||
                  '(select aa.offer_code ' ||
                    'from stlusers.STL_CONFIG_DB2F aa ' ||
                   'where aa.type_nm = ''CAR'') or tt.ordermode <> 5) ' ||
                   ') ' ||
               'and (tt.sospecnumber not in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT ' ||
                  ' where tt.type_nm=''LP'' and tt.column_nm=''product_code'') ' ||
                  'or sospecnumber is null) ' ||
               'and tt.pospecnumber not in (''50025'', ''50004'', ''9200397'') ' ||
           'union all ' ||
             'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.list_price orgfee, ' ||
                  'tt.list_price notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
             'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' ' ||
              'and tt.status = 0 ' ||
              'and tt.sospecnumber in (SELECT tt.product_code FROM stlusers.STL_CONFIG_DB2F TT  '||
                   'where tt.type_nm=''LP'' and tt.column_nm=''product_code'') ' ||
              'union all ' ||
            'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid, null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' lca ' ||
             'from int_interface_bl tt ' ||
            'where tt.remark <> ''2'' and tt.orgmonth = ''' || inMonth || ''' ' ||
              'union all ' ||
            'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'tt.orgmonth || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  ''''' adjmonth , ' ||
                  ''''' lcb ' ||
             ' from sync_interface_amount_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' and tt.status = ''0'' '||
            'union all '||
              'select ''1'' sn,    '||
                  'tt.orgbid stream_id,  '||
                  'tt.customernumber, '||
                  'tt.ordermode,   '||
                  'tt.pospecnumber,   '||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, '||
                  'tt.poid,  '||
                  'tt.soid,   '||
                  'decode(tt.dn,''0'','''',tt.dn) dn, '||
                  'tt.accountid,   '||
                  'tt.feetype, '||
                  'tt.orgfee,  '||
                  'tt.notaxfee,  '||
                  'tt.taxfee,   '||
                  'tt.taxrate, '||
                  'tt.orgmonth,   '||
                  '''' || inMonth || ''' settlemonth,  '||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, '||
                  '''2'' phase, '||
                  'tt.datasource, '||
                  'to_char(sysdate, ''yyyymmdd'') ndate, '||
                  'tt.adjmonth,  '||
                  ''''' lca  '||
             'from sync_interface_sdwan_' || inMonth || ' tt  '||
               ' where tt.pospecnumber=''50097''  and tt.status=''0'' ';


UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_203
WHERE a.CONF_ID = 203;

SELECT @iv_Sql_203;
call LOG_PROCEDURES('@iv_Sql_203执行结束',v_proc_name);

END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_204',v_proc_name);

        set @iv_Sql_204 := 'select ''5'' sn, ' ||
                  'bb.orgbid stream_id, ' ||
                  'bb.customernumber, ' ||
                  'bb.ordermode, ' ||
                  'bb.pospecnumber, ' ||
                  'bb.sospecnumber, ' ||
                  'bb.poid, ' ||
                  'bb.soid, ' ||
                  'decode(bb.dn,''0'','''',bb.dn) dn, ' ||
                  'bb.accountid, ' ||
                  'bb.feetype, ' ||
                  'bb.orgfee, ' ||
                  'bb.notaxfee, ' ||
                  'bb.taxfee, ' ||
                  'bb.taxrate, ' ||
                  'bb.orgmonth, ' ||
                  ''''' paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(bb.adjmonth,'''', bb.orgmonth,null, bb.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  'bb.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'bb.adjmonth, ' ||
                  ''''' lca ' ||
             ' from sync_interface_bl_' || inMonth || ' bb ' ||
            'where bb.status = 0 and bb.pospecnumber in ' ||
                  '(select aa.offer_code ' ||
                     'from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm = ''CAR'' ' ||
                      'and aa.column_nm = ''offer_code'')';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_204
WHERE a.CONF_ID = 204;

SELECT @iv_Sql_204;
call LOG_PROCEDURES('@iv_Sql_204执行结束',v_proc_name);

END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_205',v_proc_name);

        set @iv_Sql_205 := 'select ''2'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid,'''',tt.soid,null, tt.soid,tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.paymonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate,''yyyymmdd'')  ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
            ' from  stludr.sync_interface_ar_' || inMonth || ' tt ' ||
           'where tt.status = 0 and tt.pospecnumber not in ' ||
                  '(select aa.offer_code from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm=''CAR'') ' ||
             'and (tt.pospecnumber not in (''*********'',''********'') or tt.ordermode<>5) ' ||
             'and tt.pospecnumber not in (''50025'', ''50004'', ''9200397'') '||
       'union all ' ||
                  'select ''2'' sn, ' ||
                  'bb.orgbid  stream_id, ' ||
                  'bb.customernumber, ' ||
                  'bb.ordermode, ' ||
                  'bb.pospecnumber, ' ||
                  'decode(bb.soid,'''',bb.soid,null, bb.soid,bb.sospecnumber) sospecnumber, ' ||
                  'bb.poid, ' ||
                  'bb.soid, ' ||
                  'decode(bb.dn,''0'','''',bb.dn) dn, ' ||
                  'bb.accountid, ' ||
                  'bb.feetype, ' ||
                  'bb.orgfee, ' ||
                  'bb.notaxfee, ' ||
                  'bb.taxfee, ' ||
                  'bb.taxrate, ' ||
                  'bb.orgmonth, ' ||
                  'bb.orgmonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(bb.adjmonth,'''', bb.orgmonth,null, bb.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'bb.datasource, ' ||
                  'to_char(sysdate,''yyyymmdd'') ndate, ' ||
                  'bb.adjmonth, ' ||
                  ''''' space ' ||
           ' from stludr.sync_interface_bl_' || inMonth || ' bb ' ||
           'where bb.ordermode in (''3'',''4'') ' ||
             'and bb.status = 0 ' ||
             'and (bb.pospecnumber in ' ||
                  '(select aa.offer_code from stl_config_db2f aa ' ||
                    'where aa.type_nm=''SIM'' and aa.column_nm=''offer_code'' ' ||
                      'and ''' || inMonth || ''' between aa.eff_month and aa.exp_month) ' ||
               'or bb.sospecnumber in(select aa.product_code from stl_config_db2f aa ' ||
                                     'where aa.type_nm=''SIM'' and aa.column_nm=''product_code'' ' ||
                                       'and ''' || inMonth || ''' between aa.eff_month and aa.exp_month)) ' ||
             'and (bb.pospecnumber <> ''0102001'' or bb.soid is not null) ' ||
             'and bb.pospecnumber not in (''*********'', ''*********'', ''50022'', ''50025'', ''50004'') ' ||
       'union all '||
        -- 成员视频彩铃-省间结算
            'select ''2'' sn,       '||
                  'bb.orgbid stream_id, '||
                  'bb.customernumber,  '||
                  'bb.ordermode,   '||
                  'bb.pospecnumber,   '||
                  'decode(bb.soid,'''',bb.soid,null, bb.soid,bb.sospecnumber) sospecnumber, '||
                  'bb.poid,   '||
                  'bb.soid,   '||
                  'decode(bb.dn,''0'','''',bb.dn) dn,  '||
                  'bb.accountid,    '||
                  'decode(bb.feetype,''3682'',''362'',''3683'',''363'',''3684'',''364'',''3685'',''365'',''3865'',''568'') feetype,   '||
                  'bb.orgfee,   '||
                  'bb.notaxfee,   '||
                  'bb.taxfee,   '||
                  'bb.taxrate,   '||
                  'bb.orgmonth,   '||
                  'bb.orgmonth paid_month,   '||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(bb.adjmonth,'''', bb.orgmonth,null, bb.orgmonth, adjmonth) || ''********'' start_time, '||
                  '''2'' phase,   '||
                  'bb.datasource,   '||
                  'to_char(sysdate,''yyyymmdd'') ndate,  '||
                  'bb.adjmonth,   '||
                  '''''  space  '||
            ' from stludr.sync_interface_sdwan_' || inMonth || ' bb ' ||
           'where bb.ordermode in (''1'',''3'',''5'')   '||
             'and bb.status = 0   '||
             'and ( bb.sospecnumber = (select aa.product_code from stl_config_db2f aa   '||
                 'where aa.type_nm=''SIM'' and aa.product_code=''910401'' and aa.column_nm=''product_code''   '||
                       'and ''' || inMonth || ''' between aa.eff_month and aa.exp_month)) '||
       'union all ' ||
                  'select ''2'' sn, ' ||
                  'to_char(dd.stream_id) stream_id, ' ||
                  'dd.customernumber, ' ||
                  'dd.ordermode, ' ||
                  'dd.pospecnumber, ' ||
                  'decode(dd.soid,'''',dd.soid,null, dd.soid,dd.sospecnumber) sospecnumber, ' ||
                  'to_char(dd.poid) poid, ' ||
                  'to_char(dd.soid) soid, ' ||
                  'dd.dn, ' ||
                  'dd.accountid, ' ||
                  'dd.feetype, ' ||
                  'dd.amount orgfee, ' ||
                  'dd.amount notaxfee, ' ||
                  '0 taxfee, ' ||
                  '''6'' taxrate, ' ||
                  'dd.orgmonth, ' ||
                  'dd.orgmonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'dd.orgmonth||''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  '''AR'' datasource, ' ||
                  'to_char(sysdate,''yyyymmdd'') ndate, ' ||
                  ''''' adjmonth, ' ||
                  '''''  space ' ||
            ' from stludr.sync_bl_settle_' || inMonth || ' dd ' ||
           'where dd.pospecnumber <> ''50021'' and dd.ordermode = ''3'' and (dd.pospecnumber in ' ||
                  '(select tt.offer_code from stl_config_db2f tt ' ||
                    'where tt.type_nm=''SIM'' and tt.column_nm=''offer_code'' ' ||
                      'and ''' || inMonth || ''' between tt.eff_month and tt.exp_month) ' ||
              'or dd.sospecnumber in ' ||
                 '(select tt.product_code from stl_config_db2f tt ' ||
                    'where tt.type_nm=''SIM'' and tt.column_nm=''product_code'' ' ||
                      'and ''' || inMonth || ''' between tt.eff_month and tt.exp_month)) ' ||
            'and dd.status = ''0'' ' ||
            'union all ' ||
                'select ''2'' sn, ' ||
                   'max(to_char(aa.stream_id)) stream_id, ' ||
                   'aa.customernumber, ' ||
                   'aa.ordermode, ' ||
                   'aa.pospecnumber, ' ||
                   'decode(aa.soid, '''', aa.soid, null, aa.soid,aa.sospecnumber) sospecnumber, ' ||
                   'to_char(aa.poid) poid, ' ||
                   'to_char(aa.soid) soid, ' ||
                   'aa.dn, ' ||
                   'aa.accountid, ' ||
                   'aa.feetype, ' ||
                   'sum(aa.amount) orgfee, ' ||
                   'sum(aa.amount) notaxfee, ' ||
                   '0 taxfee, ' ||
                   '''9'' taxrate, ' ||
                   'aa.orgmonth , ' ||
                   'aa.orgmonth paid_month, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'aa.orgmonth || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   '''AR'' datasource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' adjmonth, ' ||
                   ''''' space ' ||
              'from stludr.sync_bl_settle_' || inMonth || ' aa ' ||
               'where aa.pospecnumber = ''50021'' and aa.ordermode = ''3'' ' ||-- --智能路由
               'and (aa.pospecnumber in ' ||
                  '(select tt.offer_code from stl_config_db2f tt ' ||
                    'where tt.type_nm=''SIM'' and tt.column_nm=''offer_code'' ' ||
                      'and ''' || inMonth || ''' between tt.eff_month and tt.exp_month) ' ||
                'or aa.sospecnumber in ' ||
                  '(select tt.product_code from stl_config_db2f tt ' ||
                    'where tt.type_nm=''SIM'' and tt.column_nm=''product_code'' ' ||
                      'and ''' || inMonth || ''' between tt.eff_month and tt.exp_month)) ' ||
               'and aa.status = ''0'' ' ||
               'group by orgmonth, customernumber, ordermode, pospecnumber, sospecnumber, poid, soid, dn, accountid, feetype ' ||
               'union all ' ||
               'select ''2'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn, ''0'', '''', tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.paymonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
             'from stludr.int_interface_ar tt where tt.paymonth = ''' || inMonth || ''' ';


BEGIN
                  set @vSql := 'create or replace view stlusers.Sql_205 as ' || @iv_Sql_205;
SELECT @vSql;
PREPARE STMT FROM @vSql;
EXECUTE STMT;
DEALLOCATE PREPARE STMT;
END;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = 'select * from stlusers.Sql_205'
WHERE a.CONF_ID = 205;

call LOG_PROCEDURES('@iv_Sql_205执行结束',v_proc_name);


END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_206',v_proc_name);

        set @iv_Sql_206 := 'select ''1'' sn, ' ||
                  'stlusers.seq_bid.nextval stream_id, ' ||
                  'll.customer_code, ' ||
                  'll.order_mode, ' ||
                  'll.product_code, ' ||
                  'll.service_code, ' ||
                  'll.prod_inst_id, ' ||
                  'll.svc_inst_id, ' ||
                  ''''' dn, ' ||
                  'll.account_id, ' ||
                  'll.feetype, ' ||
                  'll.amount as orgfee, ' ||
                  'll.amount as notaxfee, ' ||
                  '0 taxfee, ' ||
                  'll.taxrate, ' ||
                  'll.acct_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'll.acct_month || ''********'' start_time, ' ||
                  '''1'' phase, ' ||
                  '''BL'' datasource, ' ||

                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  ''''' as adjmonth, ' ||
                  ''''' as space ' ||
                  'from (select tt.customer_code, ' ||
                        'tt.order_mode, ' ||
                        'tt.product_code, ' ||
                        'tt.service_code, ' ||
                        'tt.prod_inst_id, ' ||
                        'tt.svc_inst_id, ' ||
                        'tt.account_id, ' ||
                        'tt.taxrate, ' ||
                        'tt.feetype, ' ||
                        'tt.acct_month, ' ||
                        'sum(tt.fee) as amount ' ||
                   'from stlusers.Interface_Sub_Member_' || inMonth || '_t tt ' ||
                    'where tt.status = ''0'' ' ||
                  'group by tt.customer_code, ' ||
                           'tt.order_mode, ' ||
                           'tt.product_code, ' ||
                           'tt.service_code, ' ||
                           'tt.prod_inst_id, ' ||
                           'tt.svc_inst_id, ' ||
                           'tt.account_id, ' ||
                           'tt.taxrate, ' ||
                           'tt.feetype, ' ||
                           'tt.acct_month) ll';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_206
WHERE a.CONF_ID = 206;

SELECT @iv_Sql_206;
call LOG_PROCEDURES('@iv_Sql_206执行结束',v_proc_name);

END;
/*
    BEGIN
        set @iv_Sql_207 := 'select ''8'' data_source, ' ||
                         'stlusers.seq_bid.nextval stream_id, ' ||
                         'tt.* ' ||
                    'from ' ||
                  '(select '''' customer_code, ' ||
                         '''1'' order_mode, ' ||
                         'prov_code out_prov, ' ||
                         ''''' in_prov, ' ||
                         'charge_code product_code, ' ||
                         ''''' service_code, ' ||
                         ''''' charge_item, ' ||
                         'ba_taxfee * 100 amount, ' ||
                         'ba_taxfee * 100 amount_notax, ' ||
                         '0 amount_tax, ' ||
                         '''6'' tax_rate, ' ||
                         'ba_month acct_month, ' ||
                         'ba_month settle_month, ' ||
                         'ba_month || ''********'' start_time, ' ||
                         '''BA'' file_source, ' ||
                         '2 phase, ' ||
                         'to_char(sysdate, ''yyyymmdd''), ' ||
                         ''''' ' ||
                    'from sync_ba_interface ' ||
                   'where ba_month = ' || inMonth || ' ' ||
                   'union all ' ||
                  'select ecid customer_code, ' ||
                         '''1'' order_mode, ' ||
                         'send_prov out_prov, ' ||
                         'recv_prov in_prov, ' ||
                         'product_code, ' ||
                         ''''' service_code, ' ||
                         ''''' charge_item, ' ||
                         'bal_fee amount, ' ||
                         'bal_fee amount_notax, ' ||
                         '0 amount_tax, ' ||
                         '''6'' tax_rate, ' ||
                         'bal_month acct_month, ' ||
                         'bal_month settle_month, ' ||
                         'bal_month || ''********'' start_time, ' ||
                         '''CC'' file_source, ' ||
                         '2 phase, ' ||
                         'to_char(sysdate, ''yyyymmdd''), ' ||
                         ''''' ' ||
                    'from sync_cb_interface ' ||
                   'where bal_month = ' || inMonth || ' ' ||
                   'and product_code not in (''*********'', ''*********'', ''*********'') ' ||
                   'union all ' ||
                   'select ecid customer_code, ' ||
                         '''1'' order_mode, ' ||
                         'send_prov out_prov, ' ||
                         'recv_prov in_prov, ' ||
                         'product_code, ' ||
                         ''''' service_code, ' ||
                         ''''' charge_item, ' ||
                         'bal_fee amount, ' ||
                         'bal_fee amount_notax, ' ||
                         '0 amount_tax, ' ||
                         '''6'' tax_rate, ' ||
                         'bal_month acct_month, ' ||
                         'bal_month settle_month, ' ||
                         'bal_month || ''********'' start_time, ' ||
                         '''CC'' file_source, ' ||
                         '2 phase, ' ||
                         'to_char(sysdate, ''yyyymmdd''), ' ||
                         ''''' ' ||
                    'from sync_cb_interface a, stl_cb_mode b ' ||
                   'where bal_month = ' || inMonth || ' ' ||
                   'and a.ecid = b.customer_code ' ||
                   'and a.product_code = b.offer_code ' ||
                   'and b.order_mode = ''1'' ' ||
                   'and a.product_code in (''*********'', ''*********'')) tt';

            UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
            SET a.BODY_SQL_TEXT = @iv_Sql_207
            WHERE a.CONF_ID = 207;

    END;
 */

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_209',v_proc_name);

        set @iv_Sql_209 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'tt.datasource, ' ||
                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
            ' from sync_interface_bl_' || inMonth || ' tt ' ||
            'left join (select * from stl_mnp_record_poc m where m.settlemonth = ' || inMonth ||
                                 ' and m.partid = substr(''' || inMonth || ''', 5, 2)) ss ' ||
              'on tt.dn = ss.member_code and tt.soid = ss.prod_order_id ' ||
           'where tt.remark=''7'' ' ||
             'and tt.status = 0 ' ||
             'and tt.pospecnumber = ''50025''';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_209
WHERE a.CONF_ID = 209;

SELECT @iv_Sql_209;
call LOG_PROCEDURES('@iv_Sql_209执行结束',v_proc_name);

END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_210',v_proc_name);

        set @iv_Sql_210 := 'select ''2'' sn, ' ||
                  'bb.orgbid stream_id, ' ||
                  'bb.customernumber, ' ||
                  'bb.ordermode, ' ||
                  'bb.pospecnumber, ' ||
                  'decode(bb.soid,'''',bb.soid,null, bb.soid,bb.sospecnumber) sospecnumber, ' ||
                  'bb.poid, ' ||
                  'bb.soid, ' ||
                  'decode(bb.dn,''0'','''',bb.dn) dn, ' ||
                  'bb.accountid, ' ||
                  'feetype, ' ||
                  'bb.orgfee, ' ||
                  'bb.notaxfee, ' ||
                  'bb.taxfee, ' ||
                  'bb.taxrate, ' ||
                  'bb.orgmonth, ' ||
                  'bb.orgmonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(bb.adjmonth,'''', bb.orgmonth,null, bb.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  'bb.datasource, ' ||
                  'to_char(sysdate,''yyyymmdd'') ndate, ' ||
                  'bb.adjmonth, ' ||
                  ''''' space ' ||
            ' from sync_interface_bl_' || inMonth || ' bb ' ||
            'left join (select * from stl_mnp_record_poc m where m.settlemonth = ' || inMonth ||
                                 ' and m.partid = substr(''' || inMonth || ''', 5, 2)) ss ' ||
              'on bb.dn = ss.member_code and bb.soid = ss.prod_order_id ' ||
           'where bb.ordermode in (''3'',''4'') ' ||
             'and bb.pospecnumber = ''50025'' ' ||
             'and bb.status = 0 ' ||
             'and (bb.pospecnumber in ' ||
                  '(select aa.offer_code from stlusers.STL_CONFIG_DB2F aa ' ||
                    'where aa.type_nm=''SIM'' and aa.column_nm=''offer_code'' ' ||
                      'and ''' || inMonth || ''' between aa.eff_month and aa.exp_month) ' ||
              'or bb.sospecnumber in(select aa.product_code from stlusers.STL_CONFIG_DB2F aa ' ||
                                     'where aa.type_nm=''SIM'' and aa.column_nm=''product_code'' ' ||
                                       'and ''' || inMonth || ''' between aa.eff_month and aa.exp_month)) '||
             'union all '||
             'select ''2'' sn, ' ||
             'tt.orgbid stream_id, ' ||
             'tt.customernumber, ' ||
             'tt.ordermode, ' ||
             'tt.pospecnumber, ' ||
             'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
             'tt.poid, ' ||
             'tt.soid, ' ||
             'decode(tt.dn, ''0'', '''', tt.dn) dn, ' ||
             'tt.accountid, ' ||
             'feetype, ' ||
             'tt.orgfee, ' ||
             'tt.notaxfee, ' ||
             'tt.taxfee, ' ||
             'tt.taxrate, ' ||
             'tt.orgmonth, ' ||
             'tt.paymonth paid_month, ' ||
             '''' || inMonth || ''' settlemonth, ' ||
             'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
             '''2'' phase, ' ||
             'tt.datasource, ' ||
             'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
             'tt.adjmonth, ' ||
             ''''' space ' ||
             ' from stludr.sync_interface_ar_' || inMonth || ' tt ' ||
             'left join (select * from stludr.stl_mnp_record_poc) ss ' ||
                 'on tt.dn = ss.member_code and tt.soid = ss.prod_order_id and ss.settlemonth = tt.orgmonth ' ||
                 'where tt.ordermode = ''1'' ' ||
                 'and tt.pospecnumber = ''50025'' ' ||
                 'and tt.status = 0 ';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_210
WHERE a.CONF_ID = 210;

SELECT @iv_Sql_210;
call LOG_PROCEDURES('@iv_Sql_210执行结束',v_proc_name);

END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_211',v_proc_name);

        set @iv_Sql_211 := 'select ''1'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  '''CD'' datasource, ' ||

                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
             'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' ' ||
              'and tt.status = 0 ' ||
              'and tt.pospecnumber = ''50004'' ' ||
             'union all ' ||
             'select ''1'' sn, ' ||
                   'to_char(aa.line_num) stream_id, ' ||
                   'aa.eboss_customer_code, ' ||
                   'decode(aa.prov_code, ''000'', ''1'', ''3'') order_mode, ' ||
                   'aa.product_code, ' ||
                   'aa.service_code, ' ||
                   'aa.prod_order_id, ' ||
                   'aa.order_id, ' ||
                   ''''' dn, ' ||
                   '0 accountid, ' ||
                   'decode(aa.customer_type, ''2'', ''17'', ''16'') feetype, ' ||
                   'round(aa.amount / 10) orgfee, ' ||
                   'round(aa.amount / 10) notaxfee, ' ||
                   '0 taxfee, ' ||
                   'to_char(aa.tax_rate) taxrate, ' ||
                   'to_char(aa.acct_month) orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'aa.acct_month || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   '''CD'' datasource, ' ||

                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' adjmonth, ' ||
                   ''''' space ' ||
              ' from cust_prod_info aa ' ||
             'where aa.product_code = ''9200397'' ' ||
               'and aa.acct_month = ''' || inMonth || ''' ' ||
               'union all ' ||
               'select ''1'' sn, ' ||
                   '''1'' stream_id, ' ||
                   'bb.customer_code, ' ||
                   'bb.order_mode, ' ||
                   'bb.offer_code, ' ||
                   'bb.product_code, ' ||
                   'bb.offer_order_id, ' ||
                   'bb.product_order_id, ' ||
                   ''''' dn, ' ||
                   '0 accountid, ' ||
                   'decode(bb.customer_type, ''2'', ''117'', ''116'') feetype, ' ||
                   '(bb.cmcc_fee + bb.oth_fee) * decode(bb.offer_code, ''50004'', 100, 1), ' ||
                   '(bb.cmcc_fee + bb.oth_fee) * decode(bb.offer_code, ''50004'', 100, 1), ' ||----CDN业务为直播源站类增加的全业务扩位
                   '0 taxfee, ' ||
                   '''6'' taxrate, ' ||
                   'bb.acct_month orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'bb.acct_month || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   '''CD'' datasource, ' ||

                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' adjmonth, ' ||
                   ''''' space ' ||
              ' from stl_cdn_order_fee bb ' ||
             'where acct_month = ''' || inMonth || ''' ' ||
              'union all ' ||
               'select ''1'' sn, ' ||
                   '''1'' stream_id, ' ||
                   'co.customer_code, ' ||
                   'co.order_mode, ' ||
                   'co.offer_code, ' ||
                   'co.product_code, ' ||
                   'co.offer_order_id, ' ||
                   'co.product_order_id, ' ||
                   ''''' dn, ' ||
                   '0 accountid, ' ||
                   'decode(co.customer_type, ''2'', ''117'', ''116'') feetype, ' ||
                   '(co.cmcc_fee + co.oth_fee) * decode(co.offer_code, ''50004'', 100, 1) orgfee, ' ||
                   '(co.cmcc_fee + co.oth_fee) * decode(co.offer_code, ''50004'', 100, 1) notaxfee, ' ||-- --CDN业务为直播源站类增加的全业务扩位
                   '0 taxfee, ' ||
                   '''6'' taxrate, ' ||
                   'co.acct_month orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'co.acct_month || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   '''CD'' datasource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' adjmonth, ' ||
                   ''''' space ' ||
              'from stl_cdn_order_fee_rif co ' ||
             'where acct_month = ''' || inMonth || ''''||
             ' union all ' ||
			  'select ''1'' sn, ' ||
                   '''1'' stream_id, ' ||
                   'co.customer_code, ' ||
                   'co.order_mode, ' ||
                   'co.offer_code, ' ||
                   'co.product_code, ' ||
                   'co.offer_order_id, ' ||
                   'co.product_order_id, ' ||
                   ''''' dn, ' ||
                   '0 accountid, ' ||
                   'decode(co.customer_type, ''2'', ''117'', ''116'') feetype, ' ||
                   '(co.cmcc_fee + co.oth_fee) * decode(co.offer_code, ''50004'', 100, 1) orgfee, ' ||
                   '(co.cmcc_fee + co.oth_fee) * decode(co.offer_code, ''50004'', 100, 1) notaxfee, ' ||
                   '0 taxfee, ' ||
                   '''6'' taxrate, ' ||
                   'co.acct_month orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||
                   'co.acct_month || ''********'' start_time, ' ||
                   '''2'' phase, ' ||
                   '''CD'' datasource, ' ||
                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                   ''''' adjmonth, ' ||
                   ''''' space ' ||
              'from stl_cdnappend_order_fee co ' ||
             'where acct_month = ''' || inMonth || ''''||
				' union all ' ||
					'select ''1'' sn, ' ||
					'''1'' stream_id, ' ||
					'a.EC_CODE, ' ||
					'TO_CHAR(a.ORDER_MODE) order_mode, ' ||
					'a.offer_code, ' ||
					'a.product_code, ' ||
					'TO_CHAR(a.OFFER_ORDER_ID), ' ||
					'TO_CHAR(a.PRODUCT_ORDER_ID), ' ||
					''''' dn, ' ||
					'0 accountid, ' ||
					'a.FEE_TYPE feetype,' ||
					'cast(sum(a.DATA_VALUE) as decimal(30,0)) orgfee,  ' ||
					'cast(sum(a.DATA_VALUE) as decimal(30,0)) notaxfee, ' ||
					'0 taxfee, ' ||
					'''6'' taxrate, ' ||
					'a.acct_month orgmonth, ' ||
					'''' || inMonth || ''' settlemonth, ' ||
					'a.acct_month || ''********'' start_time, ' ||
					'''2'' phase, ' ||

					'''CD'' datasource , ' ||
					'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
					''''' adjmonth, ' ||
					''''' space ' ||
              'from CDN_VAS_INFO a ' ||
              'where acct_month = ''' || inMonth || ''''||
				'GROUP BY '||
					'a.FEE_TYPE,'||
					'a.acct_month,'||
					'a.EC_CODE,'||
					'a.ORDER_MODE,'||
					'a.OFFER_CODE,'||
					'a.PRODUCT_CODE,'||
					'a.OFFER_ORDER_ID,'||
					'a.PRODUCT_ORDER_ID,'||
					'a.BUSI_TYPE '

					' union all ' ||
               ' select ''1'' sn, ' ||

                   '''1'' stream_id, ' ||

                   'co.customer_code, ' ||
                   'co.order_mode, ' ||
                   'co.offer_code, ' ||
                   'co.product_code, ' ||
                   'co.offer_order_id, ' ||
                   'co.product_order_id, ' ||


                   ''''' dn, ' ||
                   '0 accountid, ' ||


                   'co.charge_item feetype, ' ||
                   '(co.cmcc_fee + co.oth_fee) * 100 orgfee, ' ||
                   '(co.cmcc_fee + co.oth_fee) * 100 notaxfee, ' ||


                   '0 taxfee, ' ||
                   '''6'' taxrate, ' ||


                   'co.acct_month orgmonth, ' ||
                   '''' || inMonth || ''' settlemonth, ' ||

                   'co.acct_month || ''********'' start_time, ' ||


                   '''2'' phase, ' ||
                   '''CD'' phase, ' ||


                   'to_char(sysdate, ''yyyymmdd'') ndate, ' ||


                   ''''' adjmonth, ' ||
                   ''''' space ' ||


              ' from STL_HWCDN_ORDER_FEE co ' ||
             ' where acct_month = ''' || inMonth || '''' ;

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_211
WHERE a.CONF_ID = 211;

SELECT @iv_Sql_211;
call LOG_PROCEDURES('@iv_Sql_211执行结束',v_proc_name);

END;

BEGIN
call LOG_PROCEDURES('开始执行@iv_Sql_212',v_proc_name);

        set @iv_Sql_212 := 'select ''2'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.paymonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  '''CD'' datasource, ' ||

                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
             'from sync_interface_ar_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' ' ||
              'and tt.status = 0 ' ||
              'and tt.pospecnumber = ''50004'' ' ||
              'and tt.ordermode = ''1'' ' ||
             'union all ' ||
             'select ''2'' sn, ' ||
                  'tt.orgbid stream_id, ' ||
                  'tt.customernumber, ' ||
                  'tt.ordermode, ' ||
                  'tt.pospecnumber, ' ||
                  'decode(tt.soid, '''', tt.soid,null, tt.soid, tt.sospecnumber) sospecnumber, ' ||
                  'tt.poid, ' ||
                  'tt.soid, ' ||
                  'decode(tt.dn,''0'','''',tt.dn) dn, ' ||
                  'tt.accountid, ' ||
                  'tt.feetype, ' ||
                  'tt.orgfee, ' ||
                  'tt.notaxfee, ' ||
                  'tt.taxfee, ' ||
                  'tt.taxrate, ' ||
                  'tt.orgmonth, ' ||
                  'tt.orgmonth paid_month, ' ||
                  '''' || inMonth || ''' settlemonth, ' ||
                  'decode(tt.adjmonth,'''', tt.orgmonth,null, tt.orgmonth, adjmonth) || ''********'' start_time, ' ||
                  '''2'' phase, ' ||
                  '''CD'' datasource, ' ||

                  'to_char(sysdate, ''yyyymmdd'') ndate, ' ||
                  'tt.adjmonth, ' ||
                  ''''' space ' ||
             'from sync_interface_bl_' || inMonth || ' tt ' ||
            'where tt.remark <> ''2'' ' ||
              'and tt.status = 0 ' ||
              'and tt.pospecnumber = ''50004'' ' ||
              'and tt.ordermode = ''3''';

UPDATE stlusers.STL_CONFIG_DB2F_INSERT a
SET a.BODY_SQL_TEXT = @iv_Sql_212
WHERE a.CONF_ID = 212;

SELECT @iv_Sql_212;
call LOG_PROCEDURES('@iv_Sql_212执行结束',v_proc_name);

END;

COMMIT;


IF iv_Batch = '1' THEN
      call P_SETTLE_DB2F_sql_Insert(inMonth, iv_Batch, 8, outSysError, outReturn);
    ELSIF iv_Batch = '2' THEN
      call P_SETTLE_DB2F_sql_Insert(inMOnth, iv_Batch, 12, outSysError, outReturn);
ELSE
      outSysError := 'Wrong batch number!';
      outReturn := -1;
END IF;

SELECT 'procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn  as info;
call LOG_PROCEDURES('procedure ' || v_proc_name || ' completed successfully. nReturn=' || outReturn,v_proc_name);

END//

DELIMITER;
